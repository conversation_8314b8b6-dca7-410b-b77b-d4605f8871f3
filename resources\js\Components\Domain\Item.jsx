import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import { getEventValue } from "@/Util/TargetInputEvent";
import useOutsideClick from "@/Util/useOutsideClick";
import { Link, router, usePage } from "@inertiajs/react";
import { useRef, useState } from "react";
import { useAtom } from "jotai";
import { domainRefundCountAtom } from "@/State/DomainRefundState";
import { BsDatabaseFillLock } from "react-icons/bs";
import axios from "axios";

//* COMPONENTS
import AppVerificationPromptGroup from "@/Components/App/AppVerificationPromptGroupComponent";
import AppPromptEmailVerificationComponent from "@/Components/App/AppPromptEmailVerificationComponent";
import PrimaryButton from "@/Components/PrimaryButton";

import {
    MdInfoOutline,
    Md<PERSON>ock<PERSON><PERSON>,
    MdMoreVert,
    MdOutlineEdit,
    MdOutlineFlag,
    MdOutlineLock,
    MdLockPerson,
    MdAutorenew,
} from "react-icons/md";

import { TbTrash, TbTrashOff } from "react-icons/tb";

import _DomainStatus from "@/Constant/_DomainStatus";
import convertToTitleCase from "@/Util/convertToTitleCase";
import getRecentTime from "@/Util/getRecentTime";
import getRemainingTime from "@/Util/getRemainingTime";
import setDefaultDateFormat from "../../Util/setDefaultDateFormat";
import ShowMoreLess from "../ShowMoreLess";
import { StatusBadge } from "./StatusBadge";

import Modal from "@/Components/Modal";
import SecondaryButton from "../SecondaryButton";
import DangerButton from "../DangerButton";
import DomainActionModal from "./Index/DomainActionModal";
import DomainRefundWarningModal from "./Index/DomainRefundWarningModal";

//* UTILS
import UtilCheckIfHasSecuredTransaction from "@/Util/UtilCheckIfHasSecuredTransaction";

export default function DomainItems({
    index,
    item,
    isSelected,
    dateTimeFormat,
    onCheckboxChange,
    onRequestAuthcodeClicked,
}) {
    const isExpired = item.status.toLowerCase() === "expired";
    const isRedemption = item.status.toLowerCase() === "redemption";
    const user = usePage().props.auth.user;
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    // const { errors } = usePage().props;

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, item, getEventValue(e));
    };

    const onHandleOnClick = (link, method, data) => {
        setShow(false);
        router.visit(link, {
            method: method,
            data: data,
            replace: true,
            preserveState: false,
        });
    };

    const handleContactUs = () => {
        setShow(false);
        router.visit(route("contact.us"));
    };

    const handleRestoreClick = () => {
        setShow(false);
        router.post(
            route("domain.redeem.confirm"),
            {
                user_id: user.id,
                domains: [item],
            },
            {
                replace: true,
                preserveState: false,
            }
        );
    };

    const onChangeDateTimeFormat = (format, time) => {
        switch (format) {
            case 1:
                return <span>{setDefaultDateFormat(time)}</span>;
            case 2:
                if (Date.now() > time)
                    return (
                        <span className="text-red-500">
                            {getRecentTime(time)}
                        </span>
                    );
                return (
                    <span className="font-bold text-green-500">
                        {getRemainingTime(time)}
                    </span>
                );
            default:
                return <span>{setDefaultDateFormat(time)}</span>;
        }
    };

    const isLockedIn = (date) => {
        const today = new Date();
        var lockInDate = new Date(date * 1000);

        if (today < lockInDate) return true;

        return false;
    };

    const convertDate = (date) => {
        var lockInDate = new Date(date * 1000);

        return lockInDate.toDateString().split(" ").slice(1).join(" ");
    };

    const getNameserversLength = (nameservers) => {
        if (!nameservers) return 0;

        let jsonNS = JSON.parse(nameservers);
        if (!jsonNS) return 0;
        return jsonNS.length;
    };

    const getNameserversNames = (nameservers) => {
        if (!nameservers) return "";

        let jsonNS = JSON.parse(nameservers);
        if (!jsonNS) return "";
        return jsonNS.join(", ");
    };

    // console.log(item);

    const toolTipClassName =
        "absolute bottom-full left-1/2 -translate-x-1/2 mb-2 bg-primary text-white text-sm rounded px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10 shadow-lg w-max";

    const [domain, setDomain] = useState("");
    const [reason, setReason] = useState("");
    const [customReason, setCustomReason] = useState("");
    const [customReasonError, setCustomReasonError] = useState("");
    const [agreePolicy, setAgreePolicy] = useState(false);
    const [agreeGrace, setAgreeGrace] = useState(false);
    const [error, setError] = useState("");
    const [confirmingUserDeletion, setConfirmingUserDeletion] = useState(false);
    const closeModal = () => {
        setConfirmingUserDeletion(false);
        setDomain("");
        setReason("");
        setAgreePolicy(false);
        setAgreeGrace(false);
    };

    //! STATES
    const [stateShowVerificationPrompt, setStateShowVerificationPrompt] =
        useState(false);
    const [
        stateShowEmailVerificationPrompt,
        setStateShowEmailVerificationPrompt,
    ] = useState(false);
    const [stateShowDeleteLockWarning, setStateShowDeleteLockWarning] = useState(false);
    const [stateShowRefundWarning, setStateShowRefundWarning] = useState(false);
    const [domainDeleteCount, setDomainDeleteCount] = useAtom(domainRefundCountAtom);

    //! VARIABLES
    const shouldVerifyUser = UtilCheckIfHasSecuredTransaction("domainDelete");
    const domainRefundLimit = 10;

    const isDomainLockedForDelete = () => {
        return item.client_status && item.client_status.includes(_DomainStatus.STATUS.TRANSFER);
    };

    const isWithinFiveDayException = () => {
        const createdDate = new Date(item.created_at);
        const currentDate = new Date();
        const daysDifference = Math.floor((currentDate - createdDate) / (1000 * 60 * 60 * 24));
        return daysDifference <= 5;
    };

    const shouldShowDeleteLockWarning = () => {
        return isDomainLockedForDelete() && !isWithinFiveDayException();
    };

    const isNewlyRegisteredDomain = () => {
        const createdDate = new Date(item.created_at);
        const currentDate = new Date();
        const daysDifference = Math.floor((currentDate - createdDate) / (1000 * 60 * 60 * 24));
        return daysDifference <= 5;
    };

    const shouldShowRefundWarning = () => {
        return isNewlyRegisteredDomain() && domainDeleteCount < domainRefundLimit;
    };

    const clickDeleteBtn = () => {
        if (shouldShowDeleteLockWarning()) {
            setStateShowDeleteLockWarning(true);
            return;
        }

        if (shouldShowRefundWarning()) {
            setStateShowRefundWarning(true);
            return;
        }

        if (user.enable_email_otp) {
            setStateShowEmailVerificationPrompt(true);
        } else if (shouldVerifyUser == true) {
            setStateShowVerificationPrompt(true);
        } else {
            handleOnSubmit();
        }
    };

    const [errors, setErrors] = useState({});

    const handleDeleteDomainRequest = (itemReq) => {
        axios
            .post(route("domain.cancel"), {
                ...itemReq,
            })
            .then((response) => {
                // Update domain delete count if it's a newly registered domain
                if (isNewlyRegisteredDomain()) {
                    setDomainDeleteCount(prevCount => prevCount + 1);
                }

                // Clear fields
                setDomain("");
                setReason("");
                setAgreePolicy(false);
                setAgreeGrace(false);
                closeModal();
                router.visit(route("domain.cancellation.success"));
                return response;
            })
            .catch((error) => {
                if (error.response?.status === 422) {
                    setErrors(error.response.data.errors);
                } else {
                    console.log("Something went wrong.");
                }
                return error.response;
            });
    };

    function handleOnSubmit() {
        setConfirmingUserDeletion(true);
    }

    const handleConfirm = () => {
        let localErrors = {};

        if (reason === "Others" && customReason.trim() === "") {
            localErrors.custom_reason = "Please specify your reason.";
        }
        handleDeleteDomainRequest({
            domain,
            domainId: item.id,
            userID: item.user_contact_user_id,
            reason,
            custom_reason: reason === "Others" ? customReason : null,
            agree_policy: agreePolicy,
            agree_grace: agreeGrace,
        });
    };
    return (
        <tr className="hover:bg-gray-100">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    {["active", "expired", "redemption"].includes(
                        item.status.toLowerCase()
                    ) ? (
                        <>
                            <Checkbox
                                checked={isSelected}
                                disabled={false}
                                handleChange={handleCheckboxChange}
                            />
                            <Link
                                href={route("domain.view", { id: item.id })}
                                method="get"
                                as="button"
                                type="button"
                            >
                                <span className="cursor-pointer text-link">
                                    {item.name}
                                </span>
                            </Link>
                        </>
                    ) : (
                        <>
                            <div className="opacity-0 pointer-events-none">
                                <Checkbox disabled={true} />
                            </div>
                            <span>{item.name}</span>
                        </>
                    )}
                </label>
            </td>
            <td>
                <ShowMoreLess
                    condition={
                        getNameserversNames(item.nameservers).length > 15
                    }
                    item={getNameserversNames(item.nameservers)}
                />
            </td>
            <td>
                <span>{item.user_category_name}</span>
            </td>
            <td>
                <div className="inline-flex justify-between">
                    {item.client_status &&
                    item.client_status.includes(
                        _DomainStatus.STATUS.TRANSFER
                    ) ? (
                        isLockedIn(item.locked_until) ? (
                            // <div className="relative inline-block cursor-pointer group">
                            //     {/* Lock Icon */}
                            //     <MdOutlineLock className="text-danger" />

                            //     {/* Custom Tooltip on Hover */}
                            //     <div className={toolTipClassName}>
                            //         Locked until{" "}
                            //         {convertDate(item.locked_until)} <br />
                            //     </div>
                            // </div>
                            <div className="relative inline-block group tooltip-arrow">
                                <MdOutlineLock className="text-danger" />
                                <div className={toolTipClassName}>
                                    <div className="flex items-start gap-2">
                                        {/* Info Icon */}
                                        <MdInfoOutline className="text-blue-300 mt-0.5" />

                                        {/* Text Content */}
                                        <div>
                                            <div>Transfer Lock</div>
                                            <div className="mt-1 text-xs text-gray-300">
                                                Until{" "}
                                                {convertDate(item.locked_until)}
                                                , and cannot be reassigned or
                                                transferred.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            // <span title="Transfer Lock">
                            //     <MdOutlineLock className="text-danger" />
                            // </span>
                            <div className="relative inline-block group tooltip-arrow">
                                <MdOutlineLock className="text-danger" />
                                <div className={toolTipClassName}>
                                    <div className="flex items-start gap-2">
                                        {/* Info Icon */}
                                        <MdInfoOutline className="text-blue-300 mt-0.5" />

                                        {/* Text Content */}
                                        <div>
                                            <div>Transfer Lock</div>
                                            <div className="mt-1 text-xs text-gray-300">
                                                Cannot be reassigned or
                                                transferred.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )
                    ) : (
                        // <span title="Transfer Unlock">
                        //     <MdLockOpen className="text-success" />
                        // </span>
                        <div className="relative inline-block group tooltip-arrow">
                            <MdLockOpen className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-blue-300 mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Transfer Unlocked</div>
                                        <div className="mt-1 text-xs text-gray-300">
                                            Can now be reassigned or
                                            transferred.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.UPDATE) ? (
                        // <span title="Update Disabled">
                        //     <MdOutlineEditOff className="text-danger" />
                        // </span>
                        <div className="relative inline-block group tooltip-arrow">
                            <MdLockOpen className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-blue-300 mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Update Disabled</div>
                                        <div className="mt-1 text-xs text-gray-300">
                                            Locked and cannot be modified at
                                            this time.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // <span title="Update Enabled">
                        //     <MdOutlineEdit className="text-success" />
                        // </span>
                        <div className="relative inline-block group tooltip-arrow">
                            <MdOutlineEdit className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Update Enabled</div>
                                        <div className="mt-1 text-xs text-white">
                                            Unlocked and enabled for editing.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.HOLD) ? (
                        // <span title="On Hold">
                        //     <MdOutlineFlag className="text-danger" />
                        // </span>
                        <div className="relative inline-block group tooltip-arrow">
                            <MdOutlineFlag className="text-danger" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>On Hold</div>
                                        <div className="mt-1 text-xs text-white">
                                            Domain access and features are
                                            temporarily disabled.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // <span title="Active">
                        //     <MdOutlineFlag className="text-success" />
                        // </span>
                        <div className="relative inline-block group tooltip-arrow">
                            <MdOutlineFlag className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Active</div>
                                        <div className="mt-1 text-xs text-white">
                                            Verified and ready for use.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.DELETE) ? (
                        // <span title="Delete Disabled">
                        //     <TbTrashOff className="text-danger" />
                        // </span>
                        <div className="relative inline-block group tooltip-arrow">
                            <TbTrashOff className="text-danger" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Delete Disabled</div>
                                        <div className="mt-1 text-xs text-white">
                                            Deletion is locked or restricted.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // <span title="Delete Enabled">
                        //     <TbTrash className="text-success" />
                        // </span>
                        <div className="relative inline-block group tooltip-arrow">
                            <TbTrash className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Delete Enabled</div>
                                        <div className="mt-1 text-xs text-white">
                                            You can delete this domain if
                                            needed.
                                            <br />
                                            Please proceed with caution.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.RENEW) ? (
                        // <span title="Renew Disabled">
                        //     <MdPauseCircleOutline className="text-danger" />
                        // </span>
                        <div className="relative inline-block group tooltip-arrow">
                            <TbTrash className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Renew Disabled</div>
                                        <div className="mt-1 text-xs text-white">
                                            Domain's registry to reject requests
                                            to renew your domain. <br />
                                            It is an uncommon status that is
                                            usually enacted during legal
                                            disputes or when your domain is
                                            subject to deletion.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="relative inline-block group tooltip-arrow">
                            {item.client_status &&
                            item.client_status.includes(
                                _DomainStatus.STATUS.HOLD
                            ) ? (
                                <BsDatabaseFillLock className="text-red-400 ml-0.5" />
                            ) : (
                                <BsDatabaseFillLock className="text-success ml-0.5" />
                            )}
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Client Hold Status</div>
                                        <div className="mt-1 text-xs text-white">
                                            {item.client_status &&
                                            item.client_status.includes(
                                                _DomainStatus.STATUS.HOLD
                                            )
                                                ? "Domain is currently on Client Hold."
                                                : "Domain is not on Client Hold"}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </td>
            <td>
                <div className="inline-flex justify-between">
                    {/* <span>
                        <MdOutlineLock className="text-primary" />
                    </span> */}
                    {/* <div className="relative inline-block cursor-pointer group">
                        <MdOutlineLock className="text-primary" />
                        <div className={toolTipClassName}>
                            <div className="flex items-start gap-2">
                                <MdInfoOutline className="text-white mt-0.5" />
                                <div>
                                    <div>WHOIS Lock Enabled</div>
                                    <div className="mt-1 text-xs text-white">
                                        Prevents unauthorized changes to domain
                                        ownership or settings.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> */}
                    <div className="relative inline-block cursor-pointer group">
                        <Link
                            href={route("whois.search", {
                                domain: item.name,
                            })}
                            method="get"
                            as="button"
                            type="button"
                        >
                            <MdLockPerson
                                className={
                                    item.privacy_protection
                                        ? "text-success"
                                        : "text-danger"
                                }
                            />
                        </Link>
                        <div className={toolTipClassName}>
                            <div className="flex items-start gap-2">
                                <MdInfoOutline className="text-white mt-0.5" />
                                <div>
                                    <div>
                                        {item.privacy_protection
                                            ? "Privacy Protection Enabled"
                                            : "Privacy Protection Disabled"}
                                    </div>
                                    <div className="mt-1 text-xs text-white">
                                        {item.privacy_protection ? (
                                            <>
                                                Your personal information
                                                <br />
                                                is protected from public WHOIS
                                                lookup.
                                            </>
                                        ) : (
                                            <>
                                                Your domain registration
                                                information
                                                <br />
                                                is publicly visible.
                                            </>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </td>
            <td>
                <div className="flex items-center justify-center h-4 py-2">
                    <div className="relative inline-block group tooltip-arrow">
                        <MdAutorenew
                            className={
                                "-ml-5 " +
                                (item.auto_renew
                                    ? "text-success text-lg"
                                    : "text-red-400 text-lg")
                            }
                        />
                        <div className={toolTipClassName}>
                            <div className="flex items-start gap-2">
                                <MdInfoOutline className="text-white mt-0.5" />
                                <div>
                                    <div>
                                        {item.auto_renew
                                            ? "Auto-Renew Enabled"
                                            : "Auto-Renew Disabled"}
                                    </div>
                                    <div className="mt-1 text-xs text-white">
                                        {item.auto_renew
                                            ? "This domain will automatically renew on expiry."
                                            : "This domain will not renew automatically."}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </td>

            <td>{onChangeDateTimeFormat(dateTimeFormat, item.expiry)}</td>
            <td>{setDefaultDateFormat(item.created_at)}</td>
            <td>
                <span>
                    {<StatusBadge status={convertToTitleCase(item.status)} />}
                </span>
            </td>
            <td>
                {["active", "expired", "redemption"].includes(
                    item.status.toLowerCase()
                ) && (
                    <span ref={ref} className="relative">
                        <button
                            className="flex items-center"
                            onClick={() => setShow(!show)}
                        >
                            <MdMoreVert className="text-2xl rounded-full cursor-pointer hover:bg-gray-200" />
                        </button>
                        <DropDownContainer show={show}>
                            {isRedemption ? (
                                <>
                                    <button
                                        className="flex justify-start px-5 py-1 text-blue-500 hover:bg-gray-100"
                                        onClick={handleRestoreClick}
                                    >
                                        Renew
                                    </button>
                                </>
                            ) : item.client_status &&
                              item.client_status.includes(
                                  _DomainStatus.STATUS.HOLD
                              ) ? (
                                <button
                                    disabled={isExpired}
                                    className={`hover:bg-gray-100 px-5 py-1 justify-start flex items-center gap-2 ${
                                        isExpired && "text-gray-200"
                                    }`}
                                    onClick={() =>
                                        onHandleOnClick(
                                            route("whois.search"),
                                            "get",
                                            {
                                                domain: item.name,
                                            }
                                        )
                                    }
                                >
                                    Get Whois Privacy
                                </button>
                            ) : (
                                <>
                                    <button
                                        disabled={
                                            (item.client_status &&
                                                item.client_status.includes(
                                                    _DomainStatus.STATUS
                                                        .TRANSFER
                                                )) ||
                                            isExpired
                                        }
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex ${
                                            ((item.client_status &&
                                                item.client_status.includes(
                                                    _DomainStatus.STATUS
                                                        .TRANSFER
                                                )) ||
                                                isExpired) &&
                                            "text-gray-200"
                                        }`}
                                        onClick={() =>
                                            onHandleOnClick(
                                                route("domain.lock.confirm"),
                                                "post",
                                                {
                                                    user_id: user.id,
                                                    domains: [item],
                                                    domains_list: [item.id],
                                                    isDisable: true,
                                                }
                                            )
                                        }
                                    >
                                        Lock
                                    </button>
                                    <button
                                        disabled={
                                            !(
                                                item.client_status &&
                                                item.client_status.includes(
                                                    _DomainStatus.STATUS
                                                        .TRANSFER
                                                )
                                            ) || isExpired
                                        }
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex ${
                                            (!(
                                                item.client_status &&
                                                item.client_status.includes(
                                                    _DomainStatus.STATUS
                                                        .TRANSFER
                                                )
                                            ) ||
                                                isExpired) &&
                                            "text-gray-200"
                                        }`}
                                        onClick={() =>
                                            onHandleOnClick(
                                                route("domain.lock.confirm"),
                                                "post",
                                                {
                                                    user_id: user.id,
                                                    domains: [item],
                                                    domains_list: [item.id],
                                                    isDisable: false,
                                                }
                                            )
                                        }
                                    >
                                        Unlock
                                    </button>
                                    <button
                                        disabled={isExpired}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex items-center gap-2 ${
                                            isExpired && "text-gray-200"
                                        }`}
                                        onClick={() =>
                                            onHandleOnClick(
                                                route("whois.search"),
                                                "get",
                                                {
                                                    domain: item.name,
                                                }
                                            )
                                        }
                                    >
                                        Get Whois Privacy
                                    </button>
                                    <button
                                        disabled={isExpired}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex items-center gap-2 ${
                                            isExpired && "text-gray-200"
                                        }`}
                                        onClick={() => {
                                            setShow(false);
                                            router.post(
                                                route(
                                                    "domain.privacy.toggle",
                                                    item.id
                                                ),
                                                {
                                                    user_id: user.id,
                                                    privacy:
                                                        !item.privacy_protection,
                                                }
                                            );
                                        }}
                                    >
                                        <>
                                            <span>
                                                {item.privacy_protection
                                                    ? "Disable Privacy"
                                                    : "Enable Privacy"}
                                            </span>
                                        </>
                                    </button>
                                    <button
                                        disabled={false}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex`}
                                        onClick={() =>
                                            onHandleOnClick(
                                                route("domain.renew.confirm"),
                                                "post",
                                                {
                                                    user_id: user.id,
                                                    domains: [item],
                                                }
                                            )
                                        }
                                    >
                                        Renew Now
                                    </button>
                                    <button
                                        disabled={isExpired}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex items-center gap-2 ${
                                            isExpired && "text-gray-200"
                                        }`}
                                        onClick={() => {
                                            setShow(false);
                                            router.post(
                                                route(
                                                    "domain.autorenew.toggle",
                                                    item.id
                                                ),
                                                {
                                                    user_id: user.id,
                                                    auto_renew:
                                                        !item.auto_renew,
                                                }
                                            );
                                        }}
                                    >
                                        {item.auto_renew
                                            ? "Disable Auto-Renew"
                                            : "Enable Auto-Renew"}
                                    </button>
                                    <button
                                        disabled={isExpired}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex ${
                                            isExpired && "text-gray-200"
                                        }`}
                                        onClick={() =>
                                            onRequestAuthcodeClicked([item])
                                        }
                                    >
                                        Request Auth Code
                                    </button>
                                    <button
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex text-red-500`}
                                        onClick={() => clickDeleteBtn()}
                                    >
                                        Delete
                                    </button>
                                </>
                            )}
                        </DropDownContainer>

                        <AppPromptEmailVerificationComponent
                            show={stateShowEmailVerificationPrompt}
                            onClose={() =>
                                setStateShowEmailVerificationPrompt(false)
                            }
                            maxWidth="md"
                            onSubmitSuccess={() => {
                                setStateShowEmailVerificationPrompt(false);
                                if (shouldVerifyUser == true) {
                                    setStateShowVerificationPrompt(true);
                                } else {
                                    handleOnSubmit();
                                }
                            }}
                            onSubmitError={() => {}}
                        />

                        <AppVerificationPromptGroup
                            isShow={stateShowVerificationPrompt}
                            onClose={() =>
                                setStateShowVerificationPrompt(false)
                            }
                            onSubmitSuccess={handleOnSubmit}
                            onSubmitError={() => {}}
                        />

                        <Modal
                            show={confirmingUserDeletion}
                            onClose={closeModal}
                            maxWidth="3xl"
                        >
                            <div className="flex flex-col px-10 pt-5 pb-10 gap-y-6">
                                <section className="flex flex-col gap-2 pb-4 border-b border-gray-200">
                                    <div className="text-lg font-bold text-primary">
                                        Delete Domain: {item.name}
                                    </div>
                                    <div className="text-gray-600">
                                        To delete {item.name}, please provide a
                                        detailed reason for our review.
                                    </div>
                                </section>
                                <section className="flex flex-col gap-4">
                                    <div>
                                        <label className="block mb-2 text-sm font-medium text-gray-700">
                                            Type <strong>{item.name}</strong> to
                                            confirm{" "}
                                            <span className="text-red-500">
                                                *
                                            </span>
                                        </label>
                                        <input
                                            type="text"
                                            className="w-full p-3 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary"
                                            value={domain}
                                            onChange={(e) =>
                                                setDomain(e.target.value)
                                            }
                                            name="domain"
                                            placeholder={`Type "${item.name}" here`}
                                        />
                                        {errors.domain && (
                                            <p className="mt-1 text-sm text-red-600">
                                                {errors.domain}
                                            </p>
                                        )}
                                    </div>

                                    <div>
                                        <label className="block mb-2 text-sm font-medium text-gray-700">
                                            Reason for Deletion{" "}
                                            <span className="text-red-500">
                                                *
                                            </span>
                                        </label>

                                        <select
                                            className="w-full p-3 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-primary"
                                            value={reason}
                                            onChange={(e) =>
                                                setReason(e.target.value)
                                            }
                                            name="reason"
                                        >
                                            <option value="">
                                                -- Select a Reason --
                                            </option>
                                            <option value="No Longer Needed">
                                                No Longer Needed
                                            </option>
                                            <option value="Mistaken Registration">
                                                Mistaken Registration
                                            </option>
                                            <option value="Rebranding">
                                                Rebranding
                                            </option>
                                            <option value="Cost Savings">
                                                Cost Savings
                                            </option>
                                            <option value="Trademark/Legal Issues">
                                                Trademark/Legal Issues
                                            </option>
                                            <option value="Low Performance">
                                                Low Performance
                                            </option>
                                            <option value="Security/Privacy Concerns">
                                                Security/Privacy Concerns
                                            </option>
                                            <option value="Portfolio Cleanup">
                                                Portfolio Cleanup
                                            </option>
                                            <option value="Others">
                                                Others
                                            </option>
                                        </select>

                                        {reason === "Others" && (
                                            <>
                                                <textarea
                                                    className={`w-full p-3 mt-3 text-sm border rounded-md focus:ring-2 ${
                                                        errors.custom_reason
                                                            ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                                                            : "border-gray-300 focus:ring-primary focus:border-primary"
                                                    }`}
                                                    rows="4"
                                                    value={customReason}
                                                    onChange={(e) => {
                                                        const value =
                                                            e.target.value;
                                                        if (
                                                            value.length <= 500
                                                        ) {
                                                            setCustomReason(
                                                                value
                                                            );
                                                        }
                                                        if (
                                                            errors.custom_reason &&
                                                            value.trim() !== ""
                                                        ) {
                                                            setErrors(
                                                                (prev) => ({
                                                                    ...prev,
                                                                    custom_reason:
                                                                        null,
                                                                })
                                                            );
                                                        }
                                                    }}
                                                    maxLength={500}
                                                    placeholder="Please specify your reason..."
                                                    name="custom_reason"
                                                ></textarea>

                                                {errors.custom_reason && (
                                                    <p className="mt-1 text-sm text-red-600">
                                                        {errors.custom_reason}
                                                    </p>
                                                )}

                                                <p className="mt-1 text-xs text-gray-500">
                                                    {customReason.length}/500
                                                    characters
                                                </p>
                                            </>
                                        )}
                                    </div>

                                    <div className="space-y-3">
                                        <label className="flex items-start space-x-3">
                                            <Checkbox
                                                name="domainCancellationPolicy"
                                                value="agree_policy"
                                                checked={agreePolicy}
                                                handleChange={() =>
                                                    setAgreePolicy(!agreePolicy)
                                                }
                                            />
                                            <span className="text-sm text-gray-700">
                                                I have read and agree to the{" "}
                                                <a
                                                    href="/policy/domain-cancellation"
                                                    target="_blank"
                                                    className="underline text-link hover:text-primary"
                                                >
                                                    Domain Cancellation Policy
                                                </a>
                                                .
                                            </span>
                                        </label>
                                        {errors.agree_policy && (
                                            <p className="text-sm text-red-600 ml-7">
                                                {errors.agree_policy}
                                            </p>
                                        )}

                                        <label className="flex items-start space-x-3">
                                            <Checkbox
                                                name="FiveDayPeriod"
                                                value="agree_grace"
                                                checked={agreeGrace}
                                                handleChange={() =>
                                                    setAgreeGrace(!agreeGrace)
                                                }
                                            />
                                            <span className="text-sm text-gray-700">
                                                I understand the{" "}
                                                <a
                                                    href="/policy/5-days-grace"
                                                    target="_blank"
                                                    className="underline text-link hover:text-primary"
                                                >
                                                    5-Day Grace Period Policy
                                                </a>
                                                .
                                            </span>
                                        </label>
                                        {errors.agree_grace && (
                                            <p className="text-sm text-red-600 ml-7">
                                                {errors.agree_grace}
                                            </p>
                                        )}
                                    </div>

                                    {error && (
                                        <div className="p-3 border border-red-200 rounded-md bg-red-50">
                                            <p className="text-sm text-red-600">
                                                {error}
                                            </p>
                                        </div>
                                    )}
                                </section>

                                <section className="flex items-center justify-between pt-4 border-t border-gray-200">
                                    <PrimaryButton onClick={handleContactUs}>
                                        Contact Us
                                    </PrimaryButton>

                                    <div className="flex gap-3">
                                        <SecondaryButton onClick={closeModal}>
                                            Cancel
                                        </SecondaryButton>
                                        <DangerButton onClick={handleConfirm}>
                                            Delete Domain
                                        </DangerButton>
                                    </div>
                                </section>
                            </div>
                        </Modal>

                        <DomainActionModal
                            user={user}
                            hasLockedDomain={false}
                            hasLockedDomainForDelete={shouldShowDeleteLockWarning()}
                            isModalOpen={stateShowDeleteLockWarning}
                            modalType="delete"
                            closeModal={(shouldProceed) => {
                                setStateShowDeleteLockWarning(false);
                                if (shouldProceed) {
                                    if (user.enable_email_otp) {
                                        setStateShowEmailVerificationPrompt(true);
                                    } else if (shouldVerifyUser == true) {
                                        setStateShowVerificationPrompt(true);
                                    } else {
                                        handleOnSubmit();
                                    }
                                }
                            }}
                        />

                        <DomainRefundWarningModal
                            user={user}
                            isModalOpen={stateShowRefundWarning}
                            domainCount={domainDeleteCount}
                            domainRefundLimit={domainRefundLimit}
                            closeModal={(shouldProceed) => {
                                setStateShowRefundWarning(false);
                                if (shouldProceed) {
                                    if (user.enable_email_otp) {
                                        setStateShowEmailVerificationPrompt(true);
                                    } else if (shouldVerifyUser == true) {
                                        setStateShowVerificationPrompt(true);
                                    } else {
                                        handleOnSubmit();
                                    }
                                }
                            }}
                        />
                    </span>
                )}
            </td>
        </tr>
    );
}
