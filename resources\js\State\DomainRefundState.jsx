import { atom } from "jotai";
import { atomWithStorage, createJSONStorage } from "jotai/utils";

// Create custom session storage for Jotai
const sessionStore = createJSONStorage(() => sessionStorage);

// Storage key for domain refund count
const DOMAIN_REFUND_COUNT_KEY = "domainRefundCount";

// Atom for tracking domain refund count with persistent storage
export const domainRefundCountAtom = atomWithStorage(
    DOMAIN_REFUND_COUNT_KEY, 
    0, 
    sessionStore
);

// Hook for using domain refund state
export default function DomainRefundState() {
    return {
        domainRefundCountAtom
    };
}
